#!/bin/bash

# Example script for training with relative joint angles
# Usage: bash scripts/train_relative_joints.sh idp3 gr1_dex-3d 0913_relative_joints

dataset_path=/home/<USER>/projects/Improved-3D-Diffusion-Policy/training_data_example

DEBUG=False
wandb_mode=offline

alg_name=${1}
task_name=${2}
config_name=${alg_name}
addition_info=${3}
seed=0
exp_name=${task_name}-${alg_name}-${addition_info}
run_dir="data/outputs/${exp_name}_seed${seed}"

gpu_id=0
echo -e "\033[33mgpu id (to use): ${gpu_id}\033[0m"

if [ $DEBUG = True ]; then
    save_ckpt=False
    echo -e "\033[33mDebug mode!\033[0m"
else
    save_ckpt=True
    echo -e "\033[33mTrain mode\033[0m"
fi

echo -e "\033[33mexp name: ${exp_name}\033[0m"
echo -e "\033[33mrun dir: ${run_dir}\033[0m"
echo -e "\033[33mconfig file: ${config_name}\033[0m"

# Create a temporary config file with relative joints enabled
temp_config="config/task/gr1_dex-3d_relative.yaml"
cp "config/task/gr1_dex-3d.yaml" $temp_config

# Enable relative joints in the temporary config
sed -i 's/use_relative_joints: false/use_relative_joints: true/' $temp_config

export CUDA_VISIBLE_DEVICES=${gpu_id}
python train.py --config-name=${config_name} \
                task=${task_name}_relative \
                hydra.run.dir=${run_dir} \
                training.debug=$DEBUG \
                training.seed=${seed} \
                training.device="cuda:0" \
                exp_name=${exp_name} \
                logging.mode=${wandb_mode} \
                checkpoint.save_ckpt=${save_ckpt} \
                task.dataset.zarr_path=${dataset_path}

# Clean up temporary config
rm $temp_config
