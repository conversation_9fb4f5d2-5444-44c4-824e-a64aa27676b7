from typing import Dict
import torch
import numpy as np
import copy
from diffusion_policy_3d.common.pytorch_util import dict_apply
from diffusion_policy_3d.common.replay_buffer import ReplayBuffer
from diffusion_policy_3d.common.sampler import (SequenceSampler, get_val_mask, downsample_mask)
from diffusion_policy_3d.model.common.normalizer import LinearNormalizer, SingleFieldLinearNormalizer, StringNormalizer
from diffusion_policy_3d.dataset.base_dataset import BaseDataset
import diffusion_policy_3d.model.vision_3d.point_process as point_process
from termcolor import cprint

class GR1DexDataset3D(BaseDataset):
    def __init__(self,
            zarr_path,
            horizon=1,
            pad_before=0,
            pad_after=0,
            seed=42,
            val_ratio=0.0,
            max_train_episodes=None,
            task_name=None,
            num_points=4096,
            use_relative_joints=False,
            ):
        super().__init__()
        cprint(f'Loading GR1DexDataset from {zarr_path}', 'green')
        self.task_name = task_name

        self.num_points = num_points
        self.use_relative_joints = use_relative_joints


        buffer_keys = [
            'state', 
            'action',]
        
        buffer_keys.append('point_cloud')


            
        self.replay_buffer = ReplayBuffer.copy_from_path(
            zarr_path, keys=buffer_keys)
        
        val_mask = get_val_mask(
            n_episodes=self.replay_buffer.n_episodes, 
            val_ratio=val_ratio,
            seed=seed)
        train_mask = ~val_mask
        train_mask = downsample_mask(
            mask=train_mask, 
            max_n=max_train_episodes, 
            seed=seed)
        self.sampler = SequenceSampler(
            replay_buffer=self.replay_buffer, 
            sequence_length=horizon,
            pad_before=pad_before, 
            pad_after=pad_after,
            episode_mask=train_mask)
        self.train_mask = train_mask
        self.horizon = horizon
        self.pad_before = pad_before
        self.pad_after = pad_after

    def get_validation_dataset(self):
        val_set = copy.copy(self)
        val_set.sampler = SequenceSampler(
            replay_buffer=self.replay_buffer, 
            sequence_length=self.horizon,
            pad_before=self.pad_before, 
            pad_after=self.pad_after,
            episode_mask=~self.train_mask
            )
        val_set.train_mask = ~self.train_mask
        return val_set

    def get_normalizer(self, mode='limits', **kwargs):
        data = {'action': self.replay_buffer['action']}
        normalizer = LinearNormalizer()
        normalizer.fit(data=data, last_n_dims=1, mode=mode, **kwargs)

        normalizer['point_cloud'] = SingleFieldLinearNormalizer.create_identity()
        normalizer['agent_pos'] = SingleFieldLinearNormalizer.create_identity()
        
        return normalizer

    def __len__(self) -> int:
        return len(self.sampler)

    def _sample_to_data(self, sample):
        agent_pos = sample['state'][:,].astype(np.float32)
        point_cloud = sample['point_cloud'][:,].astype(np.float32)
        point_cloud = point_process.uniform_sampling_numpy(point_cloud, self.num_points)

        # Convert to relative joint angles if needed
        action = sample['action'].astype(np.float32)
        if hasattr(self, 'use_relative_joints') and self.use_relative_joints:
            action = self._convert_to_relative_joints(action, agent_pos)

        data = {
            'obs': {
                'agent_pos': agent_pos,
                'point_cloud': point_cloud,
                },
            'action': action}

        return data
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        sample = self.sampler.sample_sequence(idx)
        data = self._sample_to_data(sample)
        to_torch_function = lambda x: torch.from_numpy(x) if x.__class__.__name__ == 'ndarray' else x
        torch_data = dict_apply(data, to_torch_function)
        return torch_data

    def _convert_to_relative_joints(self, action, agent_pos):
        """
        Convert absolute joint angles to relative joint angles.

        Args:
            action: absolute joint angles of shape (T, 25)
            agent_pos: current robot state of shape (T, 32)

        Returns:
            relative joint angles of shape (T, 25)
        """
        import diffusion_policy_3d.common.gr1_action_util as action_util

        # Convert agent_pos from 32D to 25D format if needed
        if agent_pos.shape[-1] == 32:
            # Convert from 32D joint format to 25D format
            current_joints_25d = np.array([action_util.joint32_to_joint25(pos) for pos in agent_pos])
        else:
            current_joints_25d = agent_pos[:, :25]  # Assume first 25 dimensions are joint positions

        # For time series data, calculate relative change from previous timestep
        if action.shape[0] > 1:
            # Use the first timestep as reference, then calculate differences
            relative_action = np.zeros_like(action)
            relative_action[0] = action[0] - current_joints_25d[0]  # First timestep relative to current state

            # Subsequent timesteps are relative to previous action
            for t in range(1, action.shape[0]):
                relative_action[t] = action[t] - action[t-1]
        else:
            # Single timestep case
            relative_action = action - current_joints_25d

        return relative_action

    def _convert_from_relative_joints(self, relative_action, current_joints):
        """
        Convert relative joint angles back to absolute joint angles.

        Args:
            relative_action: relative joint angles of shape (T, 25)
            current_joints: current joint positions of shape (T, 25)

        Returns:
            absolute joint angles of shape (T, 25)
        """
        return relative_action + current_joints

