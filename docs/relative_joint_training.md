# 相对关节角训练指南

## 概述

本文档说明如何在 Improved-3D-Diffusion-Policy 中使用相对关节角进行训练，而不是默认的绝对关节角。

## 绝对关节角 vs 相对关节角

### 绝对关节角
- **定义**: 每个关节的绝对位置角度
- **优点**: 直接表示机器人的完整状态
- **缺点**: 对初始位置敏感，泛化能力可能较差

### 相对关节角
- **定义**: 相对于当前状态或前一时刻的关节角度变化
- **优点**: 更好的泛化能力，对初始位置不敏感
- **缺点**: 需要额外的状态跟踪

## 数据格式

### smolvla 数据集格式
- `state`: 形状为 [T, 32] 的机器人状态，包含完整的关节信息
- `action`: 形状为 [T, 25] 的动作，表示目标关节角度
- `point_cloud`: 形状为 [T, N, 6] 的点云数据

### 关节映射
根据 `gr1_action_util.py` 中的定义：
- 32维完整关节: waist(3) + head(3) + arm(7×2) + hand(6×2) = 32
- 25维使用关节: waist(1) + head(2) + arm(5×2) + hand(6×2) = 25

## 使用方法

### 1. 绝对关节角训练（默认）
```bash
bash scripts/train_policy.sh idp3 gr1_dex-3d 0913_example
```

### 2. 相对关节角训练
```bash
bash scripts/train_relative_joints.sh idp3 gr1_dex-3d 0913_relative_joints
```

或者直接使用相对关节角配置：
```bash
python train.py --config-name=idp3 task=gr1_dex-3d_relative
```

### 3. 配置文件修改
在配置文件中设置：
```yaml
dataset:
  use_relative_joints: true  # 启用相对关节角
```

## 实现细节

### 相对关节角转换
1. **时序数据处理**: 
   - 第一个时间步：相对于当前机器人状态
   - 后续时间步：相对于前一个动作

2. **格式转换**:
   - 自动处理32维到25维的关节格式转换
   - 使用 `joint32_to_joint25()` 函数

### 代码修改
主要修改在 `GR1DexDataset3D` 类中：
- 添加 `use_relative_joints` 参数
- 实现 `_convert_to_relative_joints()` 方法
- 在 `_sample_to_data()` 中应用转换

## 注意事项

1. **数据一致性**: 确保训练和推理时使用相同的关节角表示方式
2. **初始状态**: 相对关节角需要知道当前机器人状态作为参考
3. **累积误差**: 长序列预测时可能存在累积误差问题
4. **归一化**: 相对关节角的数值范围可能与绝对关节角不同，需要相应调整归一化参数

## 推理时的处理

在推理阶段，需要将相对关节角转换回绝对关节角：
```python
def convert_relative_to_absolute(relative_action, current_state):
    # 实现相对关节角到绝对关节角的转换
    return relative_action + current_state
```

## 性能对比

建议同时训练绝对关节角和相对关节角模型，比较它们在以下方面的性能：
- 训练收敛速度
- 最终任务成功率
- 对不同初始位置的泛化能力
- 长序列预测的稳定性
